import { saveAs } from 'file-saver'
import ky from 'ky'
import { unique } from 'radash'
import { toast } from 'sonner'
import { create } from 'zustand'
import { API_URL } from '@/config'
import { fitCenterRect } from '@/lib/geometry'
import {
  base64ToBlob,
  blobToBase64,
  imageCompose,
  PromptCommandParser,
  uploadImageSimple,
  webapi,
} from '@/lib/utils'
import type {
  BasemapImageClassType,
  ImageBasemapType,
  VCGImageDataItemType,
  VCGImageType,
} from './creation'
import { DiffusionStatus, JobType } from './creation'
import type { ImageLayer } from './edit'

export type CreationItemType = {
  id: string
  prompt: string
  image: string
}
export const DEFAULT_ASPECT_RATIO = '1:1'

export type JobItemType = {
  id: string
  session_id: string
  text: string
  status: DiffusionStatus
  type: JobType
  created_at: string
  updated_at: string
  is_copyright?: number
  seed: number
  comment: string
  worktop: WorktopType
  foreground: <PERSON><PERSON>ayer
  cover_url: string
  urls: UploadDataType[]
  audits: string[]
  canvas: {
    width: number
    height: number
  }
  img_pos?: {
    x: number
    y: number
    width: number
    height: number
  }
  img_url: string
  mask?: {
    url: string
  }
  remix_prompt: string
  basemap: BasemapType
}

export type BasemapType = {
  worktop: WorktopType
  foreground: ImageLayer
  imageRefs: ImageBasemapType[] | null
}

export type UploadDataType = {
  id: number
  width: number
  height: number
  url: string
  webp: string
  thumbnail: string
  no?: number
}

type UpdateJobItemType = {
  status: JobItemType['status']
  urls?: UploadDataType[]
  updated_at?: string
  comment?: string
  seed?: number
  no?: number
  audits?: string[]
}

export type DiffusionDataType = {
  id: string
  text: string
  status: DiffusionStatus
  seed: number
  comment: string
  session_id: string
  urls: UploadDataType[]
  created_at: string
  updated_at: string
  is_copyright: number
  audits?: string[]
}

export type SessionItemDataType = {
  id: string
  session_id: string
  upload_id: number
  url: string
  webp: string
  thumbnail: string
  width: number
  height: number
  created_at: string
  text: string
}

type SessionsDataType = {
  list: SessionItemDataType[]
  total: number
}

type JobsDataType = {
  list: JobItemType[]
  before_date: string
  has_more: boolean
}

interface SessionDataType extends DiffusionDataType {
  session_id: string
}

type ResponseType<T = unknown> = {
  status_code: number
  message: string
  data: T
}

export type ActiveType = 'edit' | 'retexture'

export type OperationType = 'move' | 'erase' | 'reverseErase'

export type WorktopType = {
  originalWidth: number
  originalHeight: number
  originalAspectRatio: number
  width?: number
  height?: number
}

type State = {
  activeType: ActiveType
  operationType: OperationType
  prompt: string
  isSuggesting: boolean
  aspectRatio: string
  basemap: ImageBasemapType[]
  jobList: JobItemType[]
  worktop: WorktopType | null
  foreground: ImageLayer | null
  isGenerating: boolean
  canDownload: boolean
  brushSize: number
  historyVersion: number
  // 历史记录状态
  history: {
    origin: ImageLayer | null
    past: ImageLayer[]
    present: ImageLayer | null
    future: ImageLayer[]
  }
  searchQuery: string
  searchResults: VCGImageType[]
  searchLoading: boolean
  searchError: string
}

type Actions = {
  setActiveType: (activeType: ActiveType) => void
  setOperationType: (operationType: OperationType) => void
  setWorktop: (worktop: WorktopType | null) => void
  setForeground: (
    foreground: ImageLayer | null,
    options?: { commit?: boolean; resetOrigin?: boolean }
  ) => void
  clearForeground: () => void
  setPrompt: (prompt: string) => void
  addJob: (job: JobItemType) => void
  setJobList: (jobList: JobItemType[]) => void
  clearJobList: () => void
  updateJob: (id: string, job: UpdateJobItemType) => void
  generate: (sessionId?: string, callback?: (sessionId: string) => void) => void
  retexture: (
    sessionId?: string,
    callback?: (sessionId: string) => void
  ) => void
  getLocalJob: (id: string) => JobItemType | undefined
  getJobInfo: (id: string) => Promise<ResponseType<DiffusionDataType>>
  getSessionInfo: (id: string) => Promise<ResponseType<JobItemType[]>>
  getSessions: (
    page: number,
    pageSize: number
  ) => Promise<ResponseType<SessionsDataType>>
  getAllJobs: (time?: string) => Promise<ResponseType<JobsDataType>>
  setAspectRatio: (aspectRatio: string) => void
  addImageBasemap: (basemap: ImageBasemapType[]) => void
  setBasemap: (basemap: ImageBasemapType[]) => void
  removeBasemap: (url: string, type: BasemapImageClassType | 'remix') => void
  clearBasemap: () => void
  updateImageBasemapType: (
    url: string,
    type: BasemapImageClassType,
    newType: BasemapImageClassType
  ) => void
  jobReroll: (id: string) => void
  viewJobImage: (id: string, no?: number) => void
  downloadJobImage: () => void
  suggestPrompt: () => void
  autoRemoveBackground: () => void
  removeJob: (id: string) => void
  setBrushSize: (brushSize: number) => void
  // 历史记录操作
  undo: () => void
  redo: () => void
  resetToOriginal: () => void
  saveHistory: (next?: ImageLayer) => void
  // 重置画布相关数据
  resetCanvas: () => void
  getSessionData: (
    sessionId: string,
    jobId?: string,
    no?: number
  ) => Promise<void>
  setSearchQuery: (searchQuery: string) => void
  searchImageFn: (searchQuery?: string) => void
}

const HISTORY_LIMIT = 30

export const useAdvancedEditStore = create<State & Actions>((set, get) => ({
  activeType: 'edit',
  operationType: 'move',
  prompt: '',
  isSuggesting: false,
  aspectRatio: DEFAULT_ASPECT_RATIO,
  basemap: [],
  jobList: [],
  worktop: null,
  foreground: null,
  isGenerating: false,
  canDownload: false,
  brushSize: 50,
  historyVersion: 0,
  // 历史记录初始化
  history: {
    origin: null,
    past: [],
    present: null,
    future: [],
  },
  setBrushSize: brushSize => set({ brushSize }),
  setWorktop: worktop => set({ worktop }),
  setOperationType: operationType => set({ operationType }),
  setForeground: (next, options) => {
    if (!next) {
      set({ foreground: null })
      return
    }
    const { history, historyVersion } = get()
    const { commit = true, resetOrigin = false } = options || {}

    // 基于值语义的克隆，避免引用共享
    const clone = (l: ImageLayer): ImageLayer => ({ ...l })

    const revokeIfBlob = (l: ImageLayer | null | undefined) => {
      try {
        if (l?.rmbgBase64?.startsWith('blob:')) {
          URL.revokeObjectURL(l.rmbgBase64)
        }
      } catch (_) {}
    }
    const revokeList = (list: ImageLayer[]) => {
      for (const item of list) revokeIfBlob(item)
    }

    if (resetOrigin || history.origin === null || history.present === null) {
      // 重置前清理之前的历史快照占用
      revokeIfBlob(history.present)
      revokeIfBlob(history.origin)
      revokeList(history.past)
      revokeList(history.future)
      const cloned = clone(next)
      console.info('setForeground resetOrigin', cloned)
      set({
        foreground: cloned,
        history: {
          origin: clone(cloned),
          past: [],
          present: clone(cloned),
          future: [],
        },
        historyVersion: historyVersion + 1,
      })
      return
    }

    // 仅同步不提交历史
    if (!commit) {
      console.info('setForeground commit', next)
      set({ foreground: clone(next) })
      return
    }

    // 提交式历史：push present -> past，再设置新的 present，清空 future（带历史上限）
    const newPast = history.present
      ? [...history.past, clone(history.present)]
      : history.past
    // 计算需要丢弃的超限历史并撤销其 blob URL
    if (newPast.length > HISTORY_LIMIT) {
      const dropCount = newPast.length - HISTORY_LIMIT
      const dropped = newPast.slice(0, dropCount)
      revokeList(dropped)
    }
    const cappedPast =
      newPast.length > HISTORY_LIMIT
        ? newPast.slice(newPast.length - HISTORY_LIMIT)
        : newPast
    const newPresent = clone(next)
    console.info('setForeground default', newPresent)
    set({
      foreground: clone(newPresent),
      history: {
        origin: history.origin ?? newPresent,
        past: cappedPast,
        present: newPresent,
        future: [],
      },
    })
  },
  clearForeground: () => set({ foreground: null }),
  clearJobList: () => set({ jobList: [] }),
  setPrompt: prompt => set({ prompt }),
  setAspectRatio: aspectRatio => set({ aspectRatio }),
  setActiveType: activeType => set({ activeType }),
  getLocalJob: id => {
    const { jobList } = get()
    return jobList.find(item => item.id === id)
  },
  addImageBasemap: es => {
    const { basemap } = get()
    const newBasemap = unique(
      basemap.concat(es),
      item => `${'url' in item ? item.url : ''}-${item.type}`
    )
    set({ basemap: newBasemap })
  },
  setBasemap: basemap => set({ basemap }),
  clearBasemap: () => set({ basemap: [] }),
  removeBasemap: (url, type) => {
    const { basemap } = get()
    const newBasemap = basemap.filter(
      item => !(('url' in item ? item.url : '') === url && item.type === type)
    )
    set({ basemap: newBasemap })
    // toast.info(`删除成功`)
  },
  updateImageBasemapType: (url, type, newType) => {
    if (!newType) return
    const { basemap } = get()
    const newBasemap = unique(
      basemap.map(item =>
        'url' in item && item.url === url && item.type === type
          ? { ...item, type: newType }
          : item
      ),
      item => `${'url' in item ? item.url : ''}-${item.type}`
    )
    set({ basemap: newBasemap })
  },
  addJob: job => {
    const { jobList } = get()
    set({ jobList: [job, ...jobList] })
    toast.info(`开始生成中...`)
  },
  setJobList: jobList => set({ jobList }),
  generate: async (sessionId, callback = () => {}) => {
    const { prompt, basemap, worktop, addJob, foreground } = get()

    if (!foreground || !worktop) {
      toast.error('请先上传图片')
      return
    }

    if (prompt.trim()) {
      try {
        set({ isGenerating: true })
        const p = prompt
        /**
         * url(内容 多个链接，空格分开) <prompt> --fast --ar 77:58(图像比例，宽：高) --sref url(风格 多个链接，空格分开) --cref url(人脸 多个链接，空格分开) --v 6.1(模型版本)
         *  */
        const textArr: string[] = []

        // 一次遍历收集所有类型的 URL，避免多次过滤
        const urlGroups = basemap.reduce(
          (acc, item) => {
            if (item.type === 'sref') {
              acc.sref.add(item.url)
            } else if (item.type === 'cref') {
              acc.cref.add(item.url)
            } else if (item.type === 'content' || item.type === 'remix') {
              acc.content.add(item.url)
            }
            return acc
          },
          {
            sref: new Set<string>(),
            cref: new Set<string>(),
            content: new Set<string>(),
          }
        )

        const srefUrls = Array.from(urlGroups.sref).join(' ')
        const crefUrls = Array.from(urlGroups.cref).join(' ')
        const contentUrls = Array.from(urlGroups.content).join(' ')

        if (contentUrls) {
          textArr.push(contentUrls)
        }

        // 如果 prompt 中没有 --v 7，并且 crefUrls 无数据，则 textArr 中添加 --v 7
        // 如果 prompt 中有 --v 7，并且 crefUrls 有数据，则 textArr 中不添加 --v 7
        // if (crefUrls && prompt.includes('--v 7')) {
        //   p = prompt.replace('--v 7', '')
        // }
        textArr.push(p)

        // 如果 prompt 中没有 --fast，则添加 --fast
        // if (!prompt.includes('--fast')) {
        //   textArr.push(`--fast`);
        //   jobTextArr.push(`--fast`);
        // }
        // let ar = aspectRatio;
        // 如果 prompt 中有 --ar，取出它的值，它的值类似于 --ar 1:1
        // const arMatch = prompt.match(/--ar\s+(\d+:\d+)/);
        // console.info('arMatch', arMatch);
        // if (arMatch) {
        //   ar = arMatch[1];
        // }

        // if (!prompt.includes('--ar ')) {
        //   textArr.push(`--ar ${ar}`);
        //   jobTextArr.push(`--ar ${ar}`);
        // }

        if (srefUrls && !p.includes('--sref ')) {
          textArr.push(`--sref ${srefUrls}`)
        }

        if (crefUrls && !p.includes('--cref ')) {
          textArr.push(`--cref ${crefUrls}`)
        }

        // if (!p.includes('--v ') && !crefUrls) {
        //   textArr.push(`--v 7`)
        // }

        // if (!p.includes('--turbo')) {
        //   textArr.push(`--turbo`)
        // }

        const text = textArr.join(' ')
        const { width, height } = worktop

        if (!width || !height) return
        const { url, id: uploadId, width: w, height: h, x, y } = foreground
        // 并行生成 mask 与 cover 图片
        const [{ blackAndWhiteBlob }, { blob }] = await Promise.all([
          imageCompose('advanced-move-layer', w, h),
          imageCompose('advanced-edit', width, height),
        ])
        const [cover, mask, rmbgUrl] = await Promise.all([
          uploadImageSimple(
            new File([blob], 'cover.png', { type: 'image/png' })
          ),
          uploadImageSimple(
            new File([blackAndWhiteBlob], 'mask.png', { type: 'image/png' })
          ),
          (async () => {
            const rmbg = foreground.rmbgBase64
            if (!rmbg) return null
            // 远程 URL 直接返回
            if (rmbg.startsWith('http://') || rmbg.startsWith('https://')) {
              return rmbg
            }
            try {
              // 对象 URL（内存 Blob）
              if (rmbg.startsWith('blob:')) {
                const response = await fetch(rmbg)
                const blob = await response.blob()
                const mime = blob.type || 'image/webp'
                const ext = mime.includes('webp')
                  ? 'webp'
                  : mime.includes('png')
                    ? 'png'
                    : 'jpg'
                return await uploadImageSimple(
                  new File([blob], `rmbg.${ext}`, { type: mime })
                )
              }
              // Data URL（base64）
              if (rmbg.startsWith('data:')) {
                const match = rmbg.match(/^data:(.*?);base64,(.*)$/)
                const mime = match?.[1] || 'image/png'
                const payload = match?.[2] || rmbg.split(',')[1]
                const blob = base64ToBlob(payload, mime)
                const ext = mime.includes('webp')
                  ? 'webp'
                  : mime.includes('png')
                    ? 'png'
                    : 'jpg'
                return await uploadImageSimple(
                  new File([blob], `rmbg.${ext}`, { type: mime })
                )
              }
              // 其他情况：不支持
              return null
            } catch (err) {
              console.error('rmbgBase64 处理失败，跳过上传', err)
              return null
            }
          })(),
        ])
        foreground.rmbgBase64 = rmbgUrl || undefined
        const res = await webapi
          .post('v1/upload-paint', {
            json: {
              uploadId,
              sessionId,
              remixPrompt: text,
              mask: {
                url: mask || '',
              },
              canvas: {
                width,
                height,
              },
              imgPos: {
                x,
                y,
                width: Math.round(w),
                height: Math.round(h),
              },
              imgUrl: url,
              coverUrl: cover || '',
              basemap: {
                worktop,
                foreground,
                imageRefs: basemap,
              },
            },
          })
          .json<ResponseType<SessionDataType>>()

        if (res.status_code === 1) {
          const { session_id, id, seed, comment, text } = res.data || {}

          if (sessionId) {
            addJob({
              id,
              session_id,
              text,
              type: JobType.ADVANCED_EDIT,
              status: DiffusionStatus.PROCESSING,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              seed,
              comment,
              worktop,
              foreground,
              cover_url: cover || '',
              urls: [],
              audits: [],
              canvas: {
                width,
                height,
              },
              img_url: url,
              remix_prompt: text,
              basemap: {
                worktop,
                foreground,
                imageRefs: basemap,
              },
            })
          } else {
            callback(session_id)
          }
        } else {
          toast.error(res.message)
        }
        set({ isGenerating: false })
      } catch (error) {
        set({ isGenerating: false })
        toast.error('生成失败，请重试')
        console.error('generate error', error)
      }
    }
  },
  suggestPrompt: async () => {
    const { foreground } = get()

    if (!foreground) {
      toast.error('请先上传图片')
      return
    }
    set({ isSuggesting: true })

    try {
      const { data, status_code, message } = await webapi
        .get('image/prompt', {
          searchParams: {
            image_url: foreground.url,
          },
        })
        .json<ResponseType<string>>()

      if (status_code === 1) {
        set({ prompt: data })
      } else {
        toast.error(message || '建议提示词接口调用失败，请重试')
      }
    } catch (error) {
      console.error('suggestPrompt error', error)
      toast.error('建议提示词接口调用失败，请重试')
    } finally {
      set({ isSuggesting: false })
    }
  },
  autoRemoveBackground: async () => {
    const { foreground, setForeground } = get()
    if (!foreground || foreground.type !== 'image') {
      toast.error('请先选择一个图片图层')
      return
    }
    const { base64 } = foreground

    if (!base64) {
      toast.error('请先选择一个图片图层')
      return
    }
    let b64 = base64
    if (base64.indexOf('http') === 0) {
      // 此处需要把网址转为 base64
      const res = await ky.get(base64).blob()
      // console.info('res', res);
      b64 = await blobToBase64(res)
    }
    const res = await ky
      .post(`${API_URL}/aigc/cutout`, {
        json: {
          image: b64.split(',')[1],
        },
        timeout: 180000,
      })
      .json<{
        data: {
          image: string
        }
        status: number
        message: string
      }>()

    if (res.data.image === 'ZXJyb3I=') {
      toast.error('所选图层没有合适的主体，请选择其它图层')
      return
    }
    setForeground({
      ...foreground,
      rmbgBase64: `data:image/png;base64,${res.data.image}`,
    })
  },
  viewJobImage: (id, no) => {
    const { jobList, setForeground } = get()
    const job = jobList.find(item => item.id === id)

    if (job) {
      const { worktop, type, text, foreground, urls = [] } = job
      let current: ImageLayer

      if (no !== undefined && urls[no]) {
        const imageUrl = urls[no]
        current = {
          id: Date.now(),
          type: 'image',
          aspectRatio: imageUrl.width / imageUrl.height,
          width: imageUrl.width,
          height: imageUrl.height,
          url: imageUrl.url,
          x: 0,
          y: 0,
          rotate: 0,
        }

        // 如果有 worktop，根据 worktop 尺寸适配图层
        if (worktop?.width && worktop?.height) {
          const {
            width: fittedWidth,
            height: fittedHeight,
            x: fittedX,
            y: fittedY,
          } = fitCenterRect(
            worktop.width,
            worktop.height,
            imageUrl.width,
            imageUrl.height,
            { capScaleAtOne: true, minSize: 20 }
          )

          console.info('viewJobImage 适配尺寸：', {
            original: { width: imageUrl.width, height: imageUrl.height },
            worktop: { width: worktop.width, height: worktop.height },
            fitted: {
              width: fittedWidth,
              height: fittedHeight,
              x: fittedX,
              y: fittedY,
            },
          })

          current = {
            ...current,
            width: fittedWidth,
            height: fittedHeight,
            x: fittedX,
            y: fittedY,
          }
        }
      } else {
        current = foreground
      }

      set({
        canDownload: no !== undefined && !!urls[no]?.url,
        worktop,
        operationType: 'move',
        activeType: type === JobType.RETEXTURE ? 'retexture' : 'edit',
        prompt: text,
      })

      if (current) {
        setForeground(current, { resetOrigin: true })
      }
    }
  },
  downloadJobImage: async () => {
    const { foreground } = get()

    if (foreground?.url) {
      await saveAs(foreground.url, 'image.png')
      toast.success('下载成功')
    } else {
      toast.error('请先上传图片')
    }
  },
  retexture: async (sessionId, callback = () => {}) => {
    const {
      prompt,
      basemap,
      addJob,
      worktop,
      // setForeground,
      foreground,
    } = get()

    if (!worktop || !foreground) {
      toast.error('请先上传图片')
      return
    }

    if (prompt.trim()) {
      try {
        console.info('retexture', prompt)
        set({ isGenerating: true })
        /**
         * url(内容 多个链接，空格分开) <prompt> --fast --ar 77:58(图像比例，宽：高) --sref url(风格 多个链接，空格分开) --cref url(人脸 多个链接，空格分开) --v 6.1(模型版本)
         *  */
        const textArr: string[] = []

        // 一次遍历收集所有类型的 URL，避免多次过滤
        const urlGroups = basemap.reduce(
          (acc, item) => {
            if (item.type === 'sref') {
              acc.sref.add(item.url)
            } else if (item.type === 'cref') {
              acc.cref.add(item.url)
            } else if (item.type === 'content' || item.type === 'remix') {
              acc.content.add(item.url)
            }
            return acc
          },
          {
            sref: new Set<string>(),
            cref: new Set<string>(),
            content: new Set<string>(),
          }
        )

        const srefUrls = Array.from(urlGroups.sref).join(' ')
        const crefUrls = Array.from(urlGroups.cref).join(' ')
        const contentUrls = Array.from(urlGroups.content).join(' ')

        if (contentUrls) {
          textArr.push(contentUrls)
        }
        textArr.push(prompt)

        // 如果 prompt 中没有 --fast，则添加 --fast
        // if (!prompt.includes('--fast')) {
        //   textArr.push(`--fast`)
        //   jobTextArr.push(`--fast`)
        // }
        // let ar = aspectRatio;
        // // 如果 prompt 中有 --ar，取出它的值，它的值类似于 --ar 1:1
        // const arMatch = prompt.match(/--ar\s+(\d+:\d+)/);
        // // console.info('arMatch', arMatch);
        // if (arMatch) {
        //   ar = arMatch[1];
        // }

        // if (!prompt.includes('--ar ')) {
        //   textArr.push(`--ar ${ar}`);
        //   jobTextArr.push(`--ar ${ar}`);
        // }

        if (srefUrls && !prompt.includes('--sref ')) {
          textArr.push(`--sref ${srefUrls}`)
        }

        if (crefUrls && !prompt.includes('--cref ')) {
          textArr.push(`--cref ${crefUrls}`)
        }

        // if (!prompt.includes('--v ')) {
        //   textArr.push(`--v 6.1`)
        //   jobTextArr.push(`--v 6.1`)
        // }

        const text = textArr.join(' ')
        const { id: uploadId, url: imgUrl } = foreground
        const res = await webapi
          .post('v1/editor/retexture', {
            json: {
              uploadId,
              sessionId,
              remixPrompt: text,
              imgUrl,
              coverUrl: imgUrl,
              basemap: {
                worktop,
                foreground,
                imageRefs: basemap,
              },
            },
          })
          .json<ResponseType<SessionDataType>>()

        if (res.status_code === 1) {
          const { session_id, id, seed, comment, text } = res.data || {}

          if (sessionId) {
            addJob({
              id,
              session_id,
              text,
              type: JobType.RETEXTURE,
              status: DiffusionStatus.PROCESSING,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              seed,
              comment,
              worktop,
              foreground,
              cover_url: imgUrl,
              urls: [],
              audits: [],
              canvas: {
                width: worktop.originalWidth,
                height: worktop.originalHeight,
              },
              img_url: imgUrl,
              remix_prompt: text,
              basemap: {
                worktop,
                foreground,
                imageRefs: basemap,
              },
            })
          } else {
            callback(session_id)
          }
        } else {
          toast.error(res.message)
        }
        set({ isGenerating: false })
      } catch (error) {
        set({ isGenerating: false })
        toast.error('生成失败，请重试')
        console.error('generate error', error)
      }
    }
  },
  getJobInfo: async id =>
    await webapi
      .get('v1/editor/job', {
        searchParams: {
          jobId: id,
        },
      })
      .json<ResponseType<DiffusionDataType>>(),
  getSessionInfo: async id => {
    const res = await webapi
      .get('v1/editor/detail', {
        searchParams: {
          sessionId: id,
        },
      })
      .json<ResponseType<JobItemType[]>>()

    return res
  },
  getSessions: async (page: number, pageSize: number) => {
    const res = await webapi
      .get('v1/editor/sessions', {
        searchParams: {
          page,
          page_size: pageSize,
        },
      })
      .json<ResponseType<SessionsDataType>>()

    return res
  },
  getAllJobs: async (time = '') => {
    const res = await webapi
      .get('v1/editor/list', {
        searchParams: {
          before_date: time,
          page_size: 20,
        },
      })
      .json<ResponseType<JobsDataType>>()

    return res
  },
  updateJob: (id, job) => {
    const { jobList } = get()
    set({
      jobList: jobList.map(item =>
        item.id === id ? { ...item, ...job } : item
      ),
    })
  },
  jobReroll: async () => {
    // const { addJob, getLocalJob } = get();
    // const res = await webapi.post('reroll', {
    //   json: {
    //     jobId,
    //   }
    // }).json<ResponseType<DiffusionDataType>>();
    // if (res.status_code === 1) {
    //   const { id: newId } = res.data || {};
    //   const currentJob = getLocalJob(jobId || '');
    //   if (!currentJob) {
    //     toast.error('任务不存在');
    //     return;
    //   }
    //   // updateJob(id, {
    //   //   id: newId,
    //   //   status: DiffusionStatus.PROCESSING,
    //   //   loading: true,
    //   //   updatedAt: new Date().toISOString(),
    //   //   data: undefined
    //   // });
    //   const { prompt, aspectRatio, establishingShot, seed, comment, type } = currentJob;
    // addJob({
    //   id: newId,
    //   prompt,
    //   loading: true,
    //   type: type || 'edit',
    //   status: DiffusionStatus.PROCESSING,
    //   createdAt: new Date().toISOString(),
    //   seed,
    //   comment,
    //   aspectRatio,
    //   establishingShot,
    // });
    // } else {
    //   toast.error(res.message);
    // }
  },
  removeJob: id => {
    const { jobList } = get()
    set({ jobList: jobList.filter(item => item.id !== id) })
  },
  // 历史记录操作函数
  saveHistory: next => {
    const { history, foreground } = get()
    const clone = (l: ImageLayer): ImageLayer => ({ ...l })
    const current = next ?? foreground

    if (!current) return
    const newPast = history.present
      ? [...history.past, clone(history.present)]
      : history.past
    const newHistory = {
      origin: history.origin ?? foreground,
      past: newPast,
      present: clone(current),
      future: [],
    }
    set({ history: newHistory, foreground: clone(current) })
  },
  undo: () => {
    const { history, historyVersion } = get()
    const { past, present, future } = history

    if (past.length === 0) return

    const previous = { ...past[past.length - 1] }
    const newPast = past.slice(0, -1)
    const newFuture = present ? [{ ...present }, ...future] : future

    console.info('撤销操作：', { from: present, to: previous })

    set({
      foreground: previous,
      history: {
        origin: history.origin,
        past: newPast,
        present: previous,
        future: newFuture,
      },
      historyVersion: historyVersion + 1,
    })
  },
  redo: () => {
    const { history, historyVersion } = get()
    const { past, present, future } = history

    if (future.length === 0) return

    const next = { ...future[0] }
    const newPast = present ? [...past, { ...present }] : past
    const newFuture = future.slice(1)

    console.info('恢复操作：', { from: present, to: next })

    set({
      foreground: next,
      history: {
        origin: history.origin,
        past: newPast,
        present: next,
        future: newFuture,
      },
      historyVersion: historyVersion + 1,
    })
  },
  resetToOriginal: () => {
    const { history, historyVersion, worktop } = get()
    if (!history.origin) return

    const original = { ...history.origin }

    // 如果有 worktop，重新计算居中位置
    if (worktop?.width && worktop?.height) {
      const {
        width: centeredWidth,
        height: centeredHeight,
        x: centeredX,
        y: centeredY,
      } = fitCenterRect(
        worktop.width,
        worktop.height,
        original.width,
        original.height,
        { capScaleAtOne: true, minSize: 20 }
      )

      console.info('重置到原图 - 居中适配：', {
        original: { width: original.width, height: original.height },
        centered: {
          width: centeredWidth,
          height: centeredHeight,
          x: centeredX,
          y: centeredY,
        },
      })

      original.width = centeredWidth
      original.height = centeredHeight
      original.x = centeredX
      original.y = centeredY
    } else {
      // 如果没有 worktop，直接居中到 (0, 0)
      original.x = 0
      original.y = 0
    }

    // 清理当前历史里所有 blob URL
    const revokeIfBlob = (l: ImageLayer | null | undefined) => {
      try {
        if (l?.rmbgBase64?.startsWith('blob:')) {
          URL.revokeObjectURL(l.rmbgBase64)
        }
      } catch (_) {}
    }
    const revokeList = (list: ImageLayer[]) => {
      for (const item of list) revokeIfBlob(item)
    }
    revokeIfBlob(history.present)
    revokeList(history.past)
    revokeList(history.future)

    console.info('重置到原图：', original)
    set({
      foreground: { ...original },
      history: {
        origin: { ...history.origin },
        past: [],
        present: { ...original },
        future: [],
      },
      historyVersion: historyVersion + 1,
    })
  },
  resetCanvas: () => {
    const { history } = get()
    // 清理所有历史里持有的 blob URL
    const revokeIfBlob = (l: ImageLayer | null | undefined) => {
      try {
        if (l?.rmbgBase64?.startsWith('blob:')) {
          URL.revokeObjectURL(l.rmbgBase64)
        }
      } catch (_) {}
    }
    const revokeList = (list: ImageLayer[]) => {
      for (const item of list) revokeIfBlob(item)
    }
    revokeIfBlob(history.present)
    revokeIfBlob(history.origin)
    revokeList(history.past)
    revokeList(history.future)
    set({
      activeType: 'edit',
      operationType: 'move',
      prompt: '',
      basemap: [],
      worktop: null,
      foreground: null,
      isGenerating: false,
      canDownload: false,
      brushSize: 50,
      jobList: [],
      // 重置历史记录状态
      history: {
        origin: null,
        past: [],
        present: null,
        future: [],
      },
    })
  },
  getSessionData: async (sessionId, jobId, no) => {
    const {
      getSessionInfo,
      setJobList,
      setForeground,
      foreground: globalForeground,
      worktop: globalWorktop,
      setPrompt,
    } = get()
    const { status_code, data, message } = await getSessionInfo(sessionId)

    if (status_code === 1) {
      setJobList(data)
      if (data.length > 0) {
        if (no === undefined && jobId === undefined) {
          const firstJob = data[0]
          const {
            basemap: { worktop, foreground, imageRefs },
            text,
          } = firstJob

          if (worktop && foreground) {
            if (
              !globalWorktop ||
              globalWorktop.originalAspectRatio !== worktop.originalAspectRatio
            ) {
              set({ worktop })
            }
            if (!globalForeground || globalForeground.url !== foreground.url) {
              setForeground(foreground, { resetOrigin: true })
            }
            set({ basemap: imageRefs || [] })
            const { data } = PromptCommandParser.parse(text)
            setPrompt(data?.prompt || text)
          }
        } else if (jobId) {
          const job = data.find(item => item.id === jobId)

          if (job) {
            const {
              basemap: { worktop, foreground, imageRefs },
              urls,
              text,
            } = job

            if (no === undefined) {
              if (
                !globalWorktop ||
                globalWorktop.originalAspectRatio !==
                  worktop.originalAspectRatio
              ) {
                set({ worktop })
              }
              setForeground(foreground, { resetOrigin: true })
              set({ basemap: imageRefs || [] })
            } else {
              const index = no
              const currentImage = urls[index]
              console.info('getSessionData - 处理特定图片：', {
                index,
                currentImage,
                urls,
                jobId,
              })

              let nextFg: ImageLayer

              if (currentImage) {
                nextFg = {
                  type: 'image',
                  width: currentImage.width,
                  height: currentImage.height,
                  aspectRatio: currentImage.width / currentImage.height,
                  rmbgBase64: undefined,
                  id: currentImage.id,
                  rotate: 0,
                  url: currentImage.url,
                  x: 0,
                  y: 0,
                }

                // 如果有 worktop，根据 worktop 尺寸适配图层
                if (worktop?.width && worktop?.height) {
                  const {
                    width: fittedWidth,
                    height: fittedHeight,
                    x: fittedX,
                    y: fittedY,
                  } = fitCenterRect(
                    worktop.width,
                    worktop.height,
                    currentImage.width,
                    currentImage.height,
                    { capScaleAtOne: true, minSize: 20 }
                  )

                  console.info('getSessionData 适配尺寸：', {
                    original: {
                      width: currentImage.width,
                      height: currentImage.height,
                    },
                    worktop: { width: worktop.width, height: worktop.height },
                    fitted: {
                      width: fittedWidth,
                      height: fittedHeight,
                      x: fittedX,
                      y: fittedY,
                    },
                  })

                  nextFg = {
                    ...nextFg,
                    width: fittedWidth,
                    height: fittedHeight,
                    x: fittedX,
                    y: fittedY,
                  }
                }
              } else {
                console.warn(
                  'getSessionData - 没有找到对应的图片，将尝试使用第一张图片作为备选：',
                  {
                    index,
                    urls,
                    urlsLength: urls.length,
                    foreground,
                  }
                )

                // 如果指定的 index 图片不存在，但 urls 有内容，尝试使用第一张
                if (urls.length > 0 && urls[0]) {
                  const firstImage = urls[0]
                  console.info('使用第一张图片作为备选：', firstImage)

                  nextFg = {
                    type: 'image',
                    width: firstImage.width,
                    height: firstImage.height,
                    aspectRatio: firstImage.width / firstImage.height,
                    rmbgBase64: undefined,
                    id: firstImage.id,
                    rotate: 0,
                    url: firstImage.url,
                    x: 0,
                    y: 0,
                  }

                  // 如果有 worktop，根据 worktop 尺寸适配图层
                  if (worktop?.width && worktop?.height) {
                    const {
                      width: fittedWidth,
                      height: fittedHeight,
                      x: fittedX,
                      y: fittedY,
                    } = fitCenterRect(
                      worktop.width,
                      worktop.height,
                      firstImage.width,
                      firstImage.height,
                      { capScaleAtOne: true, minSize: 20 }
                    )

                    console.info('getSessionData 备选图片适配尺寸：', {
                      original: {
                        width: firstImage.width,
                        height: firstImage.height,
                      },
                      worktop: { width: worktop.width, height: worktop.height },
                      fitted: {
                        width: fittedWidth,
                        height: fittedHeight,
                        x: fittedX,
                        y: fittedY,
                      },
                    })

                    nextFg = {
                      ...nextFg,
                      width: fittedWidth,
                      height: fittedHeight,
                      x: fittedX,
                      y: fittedY,
                    }
                  }
                } else {
                  // 最后的 fallback 才使用原始 foreground
                  console.warn('使用原始 foreground 作为最后备选')
                  nextFg = foreground
                }
              }

              if (
                !globalWorktop ||
                globalWorktop.originalAspectRatio !==
                  worktop.originalAspectRatio
              ) {
                set({ worktop })
              }
              setForeground(nextFg, { resetOrigin: true })
              set({ basemap: imageRefs || [] })
            }
            const { data } = PromptCommandParser.parse(text)
            setPrompt(data?.prompt || text)
          }
        }
      } else {
        console.info('还没有编辑任何内容')
        // toast.error('还没有编辑任何内容')
      }
    } else {
      toast.error(message)
    }
  },
  searchQuery: '',
  searchResults: [],
  searchLoading: false,
  searchError: '',
  setSearchQuery: searchQuery => set({ searchQuery }),
  searchImageFn: async (searchText = '') => {
    const { searchLoading, searchQuery } = get()

    if (searchLoading) {
      return
    }

    const query = (searchText || searchQuery).trim()

    if (!query) {
      toast.error('请输入搜索内容')
      return
    }
    set({
      searchLoading: true,
      searchResults: [],
      searchError: '',
      searchQuery: query,
    })
    try {
      const imagesRes = await webapi
        .post('search/image', {
          json: {
            text: query,
          },
        })
        .json<ResponseType<VCGImageDataItemType[]>>()
      const { status_code = 0, data = [] } = imagesRes

      if (status_code !== 1) {
        set({
          searchResults: [],
          searchLoading: false,
          // searchError: message || '图库获取图片信息接口调用失败，请稍后重试',
          searchError: '抱歉，没有找到相关的内容',
        })
        return
      }
      const images = data.map(image => {
        const { id, title, oss800, picWidth, picHeight } = image

        return {
          id,
          title,
          url: oss800,
          width: picWidth,
          height: picHeight,
        }
      })
      set({
        searchResults: images,
        searchLoading: false,
        searchError: '',
      })
    } catch (error) {
      console.error(error)
      set({
        searchResults: [],
        searchLoading: false,
        // searchError: '图库搜索接口调用失败，请稍后重试',
        searchError: '抱歉，没有找到相关的内容',
      })
    }
  },
}))
