import { useCreationStore } from '@/stores/creation'
import { Button } from './ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './ui/select'
import { Slider } from './ui/slider'

export const InputSetting = () => {
  const { aspectRatio } = useCreationStore()

  return (
    <div className="w-full grid grid-cols-2 gap-3 pl-14 2xl:pl-0">
      <div className="relative border rounded-md p-3 pt-2 bg-muted">
        <p className="text-center text-sm font-medium mb-2">图像大小</p>
        <div className="relative flex gap-2 items-center">
          <div className="relative size-32">
            <div
              className="max-w-full max-h-full mx-auto bg-white rounded-md border-2 border-black flex justify-center items-center"
              style={{
                aspectRatio: 1,
              }}
            >
              {aspectRatio}
            </div>
          </div>
          <div className="flex-1 p-4">
            <div className="py-4 flex justify-center border-b">
              <Button variant="outline" size="sm" className="rounded-r-none">
                竖屏
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-none -ml-[1px]"
              >
                正方形
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                横屏
              </Button>
            </div>
            <div className="w-full py-6">
              <Slider
                defaultValue={[50]}
                max={100}
                step={1}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-3 pt-2 bg-muted">
        <p className="text-center text-sm font-medium mb-2">美学</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              风格化
            </p>
            <div className="flex-1">
              <Slider
                defaultValue={[20]}
                max={100}
                step={1}
                min={0}
                className="w-full"
              />
            </div>
          </div>
          <div className="border-b flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              奇异性
            </p>
            <div className="flex-1">
              <Slider
                defaultValue={[0]}
                max={100}
                step={1}
                min={0}
                className="w-full"
              />
            </div>
          </div>
          <div className="flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              多样化
            </p>
            <div className="flex-1">
              <Slider
                defaultValue={[0]}
                max={100}
                step={1}
                min={0}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-3 pt-2 bg-muted">
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-1 right-2 text-muted-foreground"
        >
          重置
        </Button>
        <p className="text-center text-sm font-medium mb-2">模型</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              模型
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="outline" size="sm" className="rounded-r-none">
                Standard
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                Raw
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              版本
            </p>
            <div className="flex-1 flex justify-end gap-2 items-center">
              <div className="flex-1 flex justify-end">
                <Button variant="outline" size="sm" className="rounded-r-none">
                  Standard
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="rounded-l-none -ml-[1px]"
                >
                  Draft
                </Button>
              </div>
              <Select value="v7">
                <SelectTrigger className="max-w-24">
                  <SelectValue placeholder="选择版本" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>版本</SelectLabel>
                    <SelectItem value="v7">v7</SelectItem>
                    <SelectItem value="niji6">niji6</SelectItem>
                    <SelectItem value="v6.1">v6.1</SelectItem>
                    <SelectItem value="v6">v6</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-3 pt-2 bg-muted">
        <p className="text-center text-sm font-medium mb-2">更多设置</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              Speed
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="outline" size="sm" className="rounded-r-none">
                Relax
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-none -ml-[1px]"
              >
                Fast
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                Turbo
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4 px-2 border-b">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              Stealth
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="default" size="sm" className="rounded-r-none">
                开
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                关
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4 px-2 border-b">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              Video Resolution
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="default" size="sm" className="rounded-r-none">
                SD
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                HD
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4 px-2">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              Video Batch Size
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="outline" size="sm" className="rounded-r-none">
                1
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-none -ml-[1px]"
              >
                2
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                4
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
